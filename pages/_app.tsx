import type { AppProps } from 'next/app'
import { ThemeProvider } from 'next-themes'
import Script from 'next/script';
import '../theme/src/styles/globals.css';
import themeConfig from '../theme.config';

// 配置第三方服务的ID
const GA_MEASUREMENT_ID = 'G-1X4FPNE21L'; // Google Analytics ID
const ADSENSE_ID = 'ca-pub-2085111188331078';       // Google AdSense ID 
const CLARITY_ID = 's5gyenwia3';       // Microsoft Clarity ID

export default function App({ Component, pageProps }: AppProps) {
  const themeEnabled = themeConfig.features?.themeSwitch ?? false;
  const defaultTheme = themeConfig.features?.defaultTheme || 'light';

  return (
    <>
      {/* 加载 Twemoji 表情库 */}
      <Script src="https://cdn.jsdelivr.net/npm/twemoji@14.0.2/dist/twemoji.min.js" />

      {/* 主题切换提供者组件 */}
      <ThemeProvider 
        attribute="class" 
        defaultTheme={defaultTheme}
        enableSystem={themeEnabled}
        forcedTheme={!themeEnabled ? defaultTheme : undefined}
      >
        <Component {...pageProps} themeConfig={themeConfig} />
      </ThemeProvider>

      {/* Google AdSense 脚本 */}
      <Script async src={`https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=${ADSENSE_ID}`}
        crossOrigin="anonymous" />

      {/* Google Analytics 脚本 */}
      <Script
        strategy="afterInteractive"
        src={`https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`}
      />
      <Script id="google-analytics" strategy="afterInteractive">
        {`
          window.dataLayer = window.dataLayer || [];
          function gtag(){dataLayer.push(arguments);}
          gtag('js', new Date());
          gtag('config', '${GA_MEASUREMENT_ID}', {
            page_path: window.location.pathname,
          });
        `}
      </Script>

      {/* Microsoft Clarity 分析脚本 */}
      <Script type="text/javascript">
        {
          `
         (function(c,l,a,r,i,t,y){
          c[a] = c[a] || function () { (c[a].q = c[a].q || []).push(arguments) };
        t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
        y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
    })(window, document, "clarity", "script", "${CLARITY_ID}");
    `
        }
      </Script>

      {/* Plausible 统计脚本 */}
      <Script
        defer
        data-domain="mergerotgame.com"
        src="https://app.pageview.app/js/script.js"
      />

    </>
  )
} 